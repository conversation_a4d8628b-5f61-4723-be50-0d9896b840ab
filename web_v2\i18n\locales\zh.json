{"common": {"login": "登录", "register": "注册", "logout": "退出登录", "profile": "个人资料", "settings": "设置", "dashboard": "仪表盘", "home": "首页", "save": "保存", "cancel": "取消", "delete": "删除", "edit": "编辑", "create": "创建", "search": "搜索", "filter": "筛选", "actions": "操作", "status": "状态", "loading": "加载中...", "error": "错误", "success": "成功", "warning": "警告", "info": "信息", "confirm": "确认", "back": "返回", "next": "下一步", "previous": "上一步", "submit": "提交", "reset": "重置", "active": "活跃", "inactive": "不活跃", "none": "无", "or": "或", "day": "天", "days": "天", "count": "次数", "date": "日期", "backToHome": "返回首页", "tryAgain": "重试", "noData": "暂无数据"}, "auth": {"login": "登录", "register": "注册", "logout": "退出登录", "username": "用户名", "password": "密码", "confirmPassword": "确认密码", "email": "电子邮箱", "forgotPassword": "忘记密码", "rememberMe": "记住我", "loginSuccess": "登录成功", "registerSuccess": "注册成功", "logoutSuccess": "退出登录成功", "loginRequired": "请先登录", "verificationCode": "验证码", "sendVerificationCode": "发送验证码", "verificationCodeSent": "验证码已发送", "passwordMismatch": "两次输入的密码不一致", "invalidCredentials": "用户名或密码错误", "accountDisabled": "账号已被禁用", "accountLocked": "账号已被锁定", "accountExpired": "账号已过期", "credentialsExpired": "凭证已过期", "methods": "认证方式"}, "user": {"profile": "个人资料", "settings": "设置", "username": "用户名", "displayName": "显示名称", "email": "电子邮箱", "role": "角色", "status": "状态", "quota": "配额", "usedQuota": "已用配额", "requestCount": "请求次数", "createdAt": "创建时间", "lastLogin": "最后登录时间", "updateProfile": "更新资料", "changePassword": "修改密码", "currentPassword": "当前密码", "newPassword": "新密码", "confirmNewPassword": "确认新密码", "passwordChanged": "密码已修改", "profileUpdated": "资料已更新"}, "channel": {"title": "渠道管理", "list": "渠道列表", "create": "创建渠道", "edit": "编辑渠道", "delete": "删除渠道", "test": "测试渠道", "updateBalance": "更新余额", "id": "ID", "name": "名称", "type": "类型", "status": "状态", "balance": "余额", "models": "模型", "createdAt": "创建时间", "baseUrl": "基础URL", "key": "密钥", "group": "分组", "priority": "优先级", "weight": "权重", "modelMapping": "模型映射", "autoBan": "自动禁用", "fetchModels": "获取模型", "testSuccess": "测试成功", "testFailed": "测试失败", "deleteConfirm": "确定要删除此渠道吗？", "deleteSuccess": "渠道已删除", "createSuccess": "渠道已创建", "updateSuccess": "渠道已更新"}, "token": {"title": "令牌管理", "list": "令牌列表", "create": "创建令牌", "edit": "编辑令牌", "delete": "删除令牌", "id": "ID", "name": "名称", "key": "密钥", "status": "状态", "createdTime": "创建时间", "accessedTime": "访问时间", "expiredTime": "过期时间", "remainQuota": "剩余配额", "unlimitedQuota": "无限配额", "modelLimitsEnabled": "启用模型限制", "modelLimits": "模型限制", "allowIps": "允许的IP", "allowIpsHint": "多个IP用逗号分隔", "group": "分组", "tokenCount": "令牌数量", "tokenCountHint": "一次创建多个令牌（最多10个）", "warning": "警告：请妥善保管您的令牌，它们可以用来访问API。", "deleteConfirm": "确定要删除此令牌吗？", "deleteSuccess": "令牌已删除", "createSuccess": "令牌已创建", "updateSuccess": "令牌已更新", "unlimited": "无限", "noTokens": "暂无令牌"}, "log": {"title": "日志管理", "usage": "使用日志", "midjourney": "Midjourney日志", "task": "任务日志", "id": "ID", "user": "用户", "model": "模型", "tokens": "令牌数", "quota": "配额", "time": "时间", "status": "状态", "details": "详情", "request": "请求", "response": "响应", "promptTokens": "提示令牌", "completionTokens": "完成令牌", "totalTokens": "总令牌数", "filter": {"dateRange": "日期范围", "username": "用户名", "tokenName": "令牌名称", "model": "模型", "keyword": "关键词", "startDate": "开始日期", "endDate": "结束日期", "apply": "应用筛选", "reset": "重置筛选"}}, "dashboard": {"title": "数据仪表盘", "currentBalance": "当前余额", "historicalConsumption": "历史消耗", "requestCount": "请求次数", "statisticalQuota": "统计配额", "statisticalTokens": "统计令牌", "statisticalCount": "统计次数", "averageRPM": "平均RPM", "averageTPM": "平均TPM", "consumptionDistribution": "消耗分布", "callCountDistribution": "调用次数分布", "filter": {"dateRange": "日期范围", "username": "用户名", "apply": "应用筛选", "reset": "重置筛选", "defaultTime": "默认时间范围"}}, "chat": {"title": "聊天", "interface": "聊天界面", "chat2link": "Chat2Link", "playground": "Playground", "loading": "加载中...", "send": "发送", "clearContext": "清除上下文", "clearConfirm": "确定要清除所有聊天记录吗？", "startPrompt": "开始一个新的对话...", "inputPlaceholder": "输入消息...", "settings": {"title": "设置", "model": "模型", "temperature": "温度", "maxTokens": "最大令牌数", "topP": "Top P", "frequencyPenalty": "频率惩罚", "presencePenalty": "存在惩罚"}}, "system": {"title": "系统设置", "operation": "运营设置", "settings": "系统设置", "other": "其他设置", "status": {"title": "系统状态", "systemName": "系统名称", "version": "版本", "startTime": "启动时间", "emailVerification": "邮箱验证", "githubOauth": "GitHub OAuth", "oidc": "OIDC", "wechatLogin": "微信登录", "turnstileCheck": "Turnstile验证", "telegramOauth": "Telegram OAuth", "linuxdoOauth": "LinuxDO OAuth"}, "notice": "系统公告", "homePageContent": "首页内容", "aboutPage": "关于页面"}, "pricing": {"title": "价格", "plans": "套餐", "features": "功能", "quota": "配额", "price": "价格", "purchase": "购买", "redemption": {"title": "兑换", "code": "兑换码", "redeem": "兑换", "history": "兑换历史", "success": "兑换成功", "failed": "兑换失败", "description": "有兑换码？在这里兑换以添加配额到您的账户。"}, "topup": {"title": "充值", "amount": "金额", "paymentMethod": "支付方式", "pay": "支付", "history": "支付历史", "success": "支付成功", "failed": "支付失败"}}, "errors": {"unauthorized": "未授权", "forbidden": "禁止访问", "notFound": "未找到", "notFoundDescription": "您要查找的页面不存在或已被移动。", "serverError": "服务器错误", "serverErrorDescription": "我们的服务器出现了问题，请稍后再试。", "badRequest": "错误的请求", "invalidToken": "无效的令牌", "tokenExpired": "令牌已过期", "quotaExceeded": "配额已用完", "rateLimited": "请求频率过高", "invalidParameters": "无效的参数", "missingParameters": "缺少参数", "databaseError": "数据库错误", "networkError": "网络错误", "unknownError": "未知错误"}}