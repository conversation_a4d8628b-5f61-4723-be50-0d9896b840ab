{"common": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "profile": "Profile", "settings": "Settings", "dashboard": "Dashboard", "home": "Home", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "create": "Create", "search": "Search", "filter": "Filter", "actions": "Actions", "status": "Status", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "active": "Active", "inactive": "Inactive", "none": "None", "or": "Or", "day": "Day", "days": "Days", "count": "Count", "date": "Date", "backToHome": "Back to Home", "tryAgain": "Try Again", "noData": "No data available"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "email": "Email", "forgotPassword": "Forgot Password", "rememberMe": "Remember Me", "loginSuccess": "Login Successful", "registerSuccess": "Registration Successful", "logoutSuccess": "Logout Successful", "loginRequired": "Please login first", "verificationCode": "Verification Code", "sendVerificationCode": "Send Verification Code", "verificationCodeSent": "Verification Code Sent", "passwordMismatch": "Passwords do not match", "invalidCredentials": "Invalid username or password", "accountDisabled": "Account is disabled", "accountLocked": "Account is locked", "accountExpired": "Account has expired", "credentialsExpired": "Credentials have expired", "methods": "Authentication Methods"}, "user": {"profile": "Profile", "settings": "Settings", "username": "Username", "displayName": "Display Name", "email": "Email", "role": "Role", "status": "Status", "quota": "<PERSON><PERSON><PERSON>", "usedQuota": "Used Quota", "requestCount": "Request Count", "createdAt": "Created At", "lastLogin": "Last Login", "updateProfile": "Update Profile", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "passwordChanged": "Password Changed", "profileUpdated": "Profile Updated"}, "channel": {"title": "Channel Management", "list": "Channel List", "create": "Create Channel", "edit": "Edit Channel", "delete": "Delete Channel", "test": "Test Channel", "updateBalance": "Update Balance", "id": "ID", "name": "Name", "type": "Type", "status": "Status", "balance": "Balance", "models": "Models", "createdAt": "Created At", "baseUrl": "Base URL", "key": "Key", "group": "Group", "priority": "Priority", "weight": "Weight", "modelMapping": "Model Mapping", "autoBan": "Auto Ban", "fetchModels": "Fetch Models", "testSuccess": "Test Successful", "testFailed": "Test Failed", "deleteConfirm": "Are you sure you want to delete this channel?", "deleteSuccess": "Channel deleted", "createSuccess": "Channel created", "updateSuccess": "Channel updated"}, "token": {"title": "Token Management", "list": "Token List", "create": "Create Token", "edit": "<PERSON>", "delete": "Delete Token", "id": "ID", "name": "Name", "key": "Key", "status": "Status", "createdTime": "Created Time", "accessedTime": "Accessed Time", "expiredTime": "Expired Time", "remainQuota": "<PERSON><PERSON><PERSON>", "unlimitedQuota": "Unlimited <PERSON><PERSON>ta", "modelLimitsEnabled": "Model Limits Enabled", "modelLimits": "Model Limits", "allowIps": "Allowed IPs", "allowIpsHint": "Separate multiple IPs with commas", "group": "Group", "tokenCount": "Token Count", "tokenCountHint": "Create multiple tokens at once (max 10)", "warning": "Warning: Keep your tokens secure. They can be used to access the API.", "deleteConfirm": "Are you sure you want to delete this token?", "deleteSuccess": "<PERSON><PERSON> deleted", "createSuccess": "<PERSON><PERSON> created", "updateSuccess": "Token updated", "unlimited": "Unlimited", "noTokens": "No tokens found"}, "log": {"title": "Log Management", "usage": "Usage Logs", "midjourney": "Midjourney Logs", "task": "Task Logs", "id": "ID", "user": "User", "model": "Model", "tokens": "Tokens", "quota": "<PERSON><PERSON><PERSON>", "time": "Time", "status": "Status", "details": "Details", "request": "Request", "response": "Response", "promptTokens": "Prompt Tokens", "completionTokens": "Completion Tokens", "totalTokens": "Total Tokens", "filter": {"dateRange": "Date Range", "username": "Username", "tokenName": "Token Name", "model": "Model", "keyword": "Keyword", "startDate": "Start Date", "endDate": "End Date", "apply": "Apply Filters", "reset": "Reset Filters"}}, "dashboard": {"title": "Data Dashboard", "currentBalance": "Current Balance", "historicalConsumption": "Historical Consumption", "requestCount": "Request Count", "statisticalQuota": "Statistical Quota", "statisticalTokens": "Statistical Tokens", "statisticalCount": "Statistical Count", "averageRPM": "Average RPM", "averageTPM": "Average TPM", "consumptionDistribution": "Consumption Distribution", "callCountDistribution": "Call Count Distribution", "filter": {"dateRange": "Date Range", "username": "Username", "apply": "Apply Filters", "reset": "Reset Filters", "defaultTime": "Default Time Range"}}, "chat": {"title": "Cha<PERSON>", "interface": "Chat Interface", "chat2link": "Chat2Link", "playground": "Playground", "loading": "Loading...", "send": "Send", "clearContext": "Clear Context", "clearConfirm": "Are you sure you want to clear all chat messages?", "startPrompt": "Start a new conversation...", "inputPlaceholder": "Type a message...", "settings": {"title": "Settings", "model": "Model", "temperature": "Temperature", "maxTokens": "<PERSON>", "topP": "Top P", "frequencyPenalty": "Frequency Penalty", "presencePenalty": "Presence Penalty"}}, "system": {"title": "System Settings", "operation": "Operation Settings", "settings": "System Settings", "other": "Other Settings", "status": {"title": "System Status", "systemName": "System Name", "version": "Version", "startTime": "Start Time", "emailVerification": "Email Verification", "githubOauth": "GitHub OAuth", "oidc": "OIDC", "wechatLogin": "<PERSON><PERSON><PERSON>", "turnstileCheck": "Turnstile Check", "telegramOauth": "Telegram OAuth", "linuxdoOauth": "LinuxDO OAuth"}, "notice": "System Notice", "homePageContent": "Home Page Content", "aboutPage": "About Page"}, "pricing": {"title": "Pricing", "plans": "Plans", "features": "Features", "quota": "<PERSON><PERSON><PERSON>", "price": "Price", "purchase": "Purchase", "redemption": {"title": "Redemption", "code": "Redemption Code", "redeem": "Redeem", "history": "Redemption History", "success": "Redemption Successful", "failed": "Redemption Failed", "description": "Have a redemption code? Redeem it here to add quota to your account."}, "topup": {"title": "Top Up", "amount": "Amount", "paymentMethod": "Payment Method", "pay": "Pay", "history": "Payment History", "success": "Payment Successful", "failed": "Payment Failed"}}, "errors": {"unauthorized": "Unauthorized", "forbidden": "Forbidden", "notFound": "Not Found", "notFoundDescription": "The page you are looking for does not exist or has been moved.", "serverError": "Server Error", "serverErrorDescription": "Something went wrong on our end. Please try again later.", "badRequest": "Bad Request", "invalidToken": "Invalid <PERSON>", "tokenExpired": "Token Expired", "quotaExceeded": "<PERSON><PERSON><PERSON> Exceeded", "rateLimited": "Rate Limited", "invalidParameters": "Invalid Parameters", "missingParameters": "Missing Parameters", "databaseError": "Database Error", "networkError": "Network Error", "unknownError": "Unknown Error"}}