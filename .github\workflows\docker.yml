name: Publish Docker images

on:
  push:
    tags:
      - '*'
  workflow_dispatch:
    inputs:
      reason:
        description: 'Reason for manual trigger'
        required: false

jobs:
  push_to_registries:
    name: Build and push Docker images
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Save version info
        run: |
          git describe --tags > VERSION 

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          # Use buildx driver with multiple concurrent builders
          driver-opts: |
            network=host
          # Enable BuildKit cache
          buildkitd-flags: --debug

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Login to Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: |
            ghcr.io/${{ github.repository }}
          tags: |
            type=ref,event=tag
            type=raw,value=latest,enable=${{ !endsWith(github.ref, '-alpha') && !endsWith(github.ref, '-beta') }}
            type=raw,value=alpha,enable=${{ endsWith(github.ref, '-alpha') }}
            type=raw,value=beta,enable=${{ endsWith(github.ref, '-beta') }}

      - name: Build and push Docker images
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          # Use cache for faster builds
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
          # Optimize build performance
          build-args: |
            BUILDKIT_INLINE_CACHE=1
          # Parallel processing
          builder: ${{ steps.buildx.outputs.name }}
          # Set output to suppress verbose logs
          outputs: type=image,name=target,annotation-index.org.opencontainers.image.description=Docker image for ${{ github.repository }}

      # Temp fix for https://github.com/docker/build-push-action/issues/252
      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache