openapi: 3.0.0
info:
  title: Veloera
  description: API documentation for the Veloera platform
  version: 1.0.0
servers:
  - url: /api
    description: API base path
tags:
  - name: Authentication
    description: User authentication endpoints
  - name: User
    description: User management endpoints
  - name: Token
    description: Token management endpoints
  - name: Channel
    description: Channel management endpoints
  - name: Log
    description: Log and usage data endpoints
  - name: Midjourney
    description: Midjourney image generation endpoints
  - name: Task
    description: Task management endpoints
  - name: System
    description: System information and settings endpoints

paths:
  /user/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticates a user with username and password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: User's username
                password:
                  type: string
                  description: User's password
                turnstile:
                  type: string
                  description: Turnstile verification token (if enabled)
      responses:
        '200':
          description: Login response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                      username:
                        type: string
                      display_name:
                        type: string
                      role:
                        type: integer
                      status:
                        type: integer
                      quota:
                        type: integer
                      used_quota:
                        type: integer
                      request_count:
                        type: integer
                      token:
                        type: string
                        description: JWT token for authentication

  /user/register:
    post:
      tags:
        - Authentication
      summary: User registration
      description: Registers a new user account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: User's username
                password:
                  type: string
                  description: User's password
                email:
                  type: string
                  description: User's email (required if email verification is enabled)
                verification_code:
                  type: string
                  description: Email verification code (required if email verification is enabled)
                turnstile:
                  type: string
                  description: Turnstile verification token (if enabled)
      responses:
        '200':
          description: Registration response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string

  /user/self:
    get:
      tags:
        - User
      summary: Get current user information
      description: Retrieves information about the currently authenticated user
      security:
        - bearerAuth: []
      responses:
        '200':
          description: User information response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                      username:
                        type: string
                      display_name:
                        type: string
                      email:
                        type: string
                      role:
                        type: integer
                      status:
                        type: integer
                      quota:
                        type: integer
                      used_quota:
                        type: integer
                      request_count:
                        type: integer
                      created_at:
                        type: string
                        format: date-time
    put:
      tags:
        - User
      summary: Update current user information
      description: Updates information for the currently authenticated user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                display_name:
                  type: string
                  description: User's display name
                password:
                  type: string
                  description: User's new password
                email:
                  type: string
                  description: User's email
      responses:
        '200':
          description: Update response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string

  /token:
    get:
      tags:
        - Token
      summary: Get all tokens for current user
      description: Retrieves all API tokens for the currently authenticated user
      security:
        - bearerAuth: []
      parameters:
        - name: p
          in: query
          description: Page number
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Items per page
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Tokens response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                        name:
                          type: string
                        key:
                          type: string
                        status:
                          type: integer
                        created_time:
                          type: integer
                          format: int64
                        accessed_time:
                          type: integer
                          format: int64
                        expired_time:
                          type: integer
                          format: int64
                        remain_quota:
                          type: integer
                        unlimited_quota:
                          type: boolean
                        model_limits_enabled:
                          type: boolean
                        model_limits:
                          type: string
                        allow_ips:
                          type: string
                        group:
                          type: string
    post:
      tags:
        - Token
      summary: Create a new token
      description: Creates a new API token for the currently authenticated user
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
              properties:
                name:
                  type: string
                  description: Token name
                remain_quota:
                  type: integer
                  description: Remaining quota for the token
                unlimited_quota:
                  type: boolean
                  description: Whether the token has unlimited quota
                expired_time:
                  type: integer
                  format: int64
                  description: Token expiration time (unix timestamp)
                model_limits_enabled:
                  type: boolean
                  description: Whether model limits are enabled
                model_limits:
                  type: string
                  description: Comma-separated list of allowed models
                allow_ips:
                  type: string
                  description: Comma-separated list of allowed IPs
                group:
                  type: string
                  description: Token group
      responses:
        '200':
          description: Creation response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
    put:
      tags:
        - Token
      summary: Update a token
      description: Updates an existing API token
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - name
              properties:
                id:
                  type: integer
                  description: Token ID
                name:
                  type: string
                  description: Token name
                remain_quota:
                  type: integer
                  description: Remaining quota for the token
                unlimited_quota:
                  type: boolean
                  description: Whether the token has unlimited quota
                expired_time:
                  type: integer
                  format: int64
                  description: Token expiration time (unix timestamp)
                model_limits_enabled:
                  type: boolean
                  description: Whether model limits are enabled
                model_limits:
                  type: string
                  description: Comma-separated list of allowed models
                allow_ips:
                  type: string
                  description: Comma-separated list of allowed IPs
                group:
                  type: string
                  description: Token group
      responses:
        '200':
          description: Update response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string

  /token/{id}:
    get:
      tags:
        - Token
      summary: Get token by ID
      description: Retrieves a specific token by its ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Token ID
          schema:
            type: integer
      responses:
        '200':
          description: Token response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                      name:
                        type: string
                      key:
                        type: string
                      status:
                        type: integer
                      created_time:
                        type: integer
                        format: int64
                      accessed_time:
                        type: integer
                        format: int64
                      expired_time:
                        type: integer
                        format: int64
                      remain_quota:
                        type: integer
                      unlimited_quota:
                        type: boolean
                      model_limits_enabled:
                        type: boolean
                      model_limits:
                        type: string
                      allow_ips:
                        type: string
                      group:
                        type: string
    delete:
      tags:
        - Token
      summary: Delete token
      description: Deletes a specific token by its ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Token ID
          schema:
            type: integer
      responses:
        '200':
          description: Deletion response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string

  /channel:
    get:
      tags:
        - Channel
      summary: Get all channels
      description: Retrieves all channels (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: p
          in: query
          description: Page number
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Items per page
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Channels response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                        name:
                          type: string
                        type:
                          type: integer
                        status:
                          type: integer
                        balance:
                          type: number
                          format: float
                        models:
                          type: string
                        group:
                          type: string
                        created_at:
                          type: string
                          format: date-time
    post:
      tags:
        - Channel
      summary: Create a new channel
      description: Creates a new channel (admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - name
                - type
                - key
              properties:
                name:
                  type: string
                  description: Channel name
                type:
                  type: integer
                  description: Channel type
                base_url:
                  type: string
                  description: Base URL for the channel
                key:
                  type: string
                  description: API key for the channel
                models:
                  type: string
                  description: Comma-separated list of supported models
                group:
                  type: string
                  description: Comma-separated list of groups
                priority:
                  type: integer
                  description: Channel priority
                weight:
                  type: integer
                  description: Channel weight
                model_mapping:
                  type: string
                  description: JSON string of model mappings
                auto_ban:
                  type: integer
                  description: Whether to auto-ban the channel on failure
      responses:
        '200':
          description: Creation response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
    put:
      tags:
        - Channel
      summary: Update a channel
      description: Updates an existing channel (admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - id
                - name
                - type
                - key
              properties:
                id:
                  type: integer
                  description: Channel ID
                name:
                  type: string
                  description: Channel name
                type:
                  type: integer
                  description: Channel type
                base_url:
                  type: string
                  description: Base URL for the channel
                key:
                  type: string
                  description: API key for the channel
                models:
                  type: string
                  description: Comma-separated list of supported models
                group:
                  type: string
                  description: Comma-separated list of groups
                priority:
                  type: integer
                  description: Channel priority
                weight:
                  type: integer
                  description: Channel weight
                model_mapping:
                  type: string
                  description: JSON string of model mappings
                auto_ban:
                  type: integer
                  description: Whether to auto-ban the channel on failure
      responses:
        '200':
          description: Update response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string

  /channel/{id}:
    get:
      tags:
        - Channel
      summary: Get channel by ID
      description: Retrieves a specific channel by its ID (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Channel ID
          schema:
            type: integer
      responses:
        '200':
          description: Channel response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      id:
                        type: integer
                      name:
                        type: string
                      type:
                        type: integer
                      base_url:
                        type: string
                      key:
                        type: string
                      status:
                        type: integer
                      models:
                        type: string
                      group:
                        type: string
                      priority:
                        type: integer
                      weight:
                        type: integer
                      model_mapping:
                        type: string
                      auto_ban:
                        type: integer
    delete:
      tags:
        - Channel
      summary: Delete channel
      description: Deletes a specific channel by its ID (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Channel ID
          schema:
            type: integer
      responses:
        '200':
          description: Deletion response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string

  /channel/test/{id}:
    get:
      tags:
        - Channel
      summary: Test channel
      description: Tests a specific channel by its ID (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: Channel ID
          schema:
            type: integer
      responses:
        '200':
          description: Test response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string

  /channel/fetch_models:
    post:
      tags:
        - Channel
      summary: Fetch models from upstream
      description: Fetches available models from an upstream provider (admin only)
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - type
                - key
              properties:
                base_url:
                  type: string
                  description: Base URL for the channel
                type:
                  type: integer
                  description: Channel type
                key:
                  type: string
                  description: API key for the channel
      responses:
        '200':
          description: Models response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      type: string

  /log:
    get:
      tags:
        - Log
      summary: Get all logs
      description: Retrieves all usage logs (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: p
          in: query
          description: Page number
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Items per page
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Logs response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Log'

  /log/search:
    get:
      tags:
        - Log
      summary: Search logs
      description: Searches logs with filters (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: p
          in: query
          description: Page number
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Items per page
          schema:
            type: integer
            default: 10
        - name: username
          in: query
          description: Filter by username
          schema:
            type: string
        - name: token_name
          in: query
          description: Filter by token name
          schema:
            type: string
        - name: model
          in: query
          description: Filter by model
          schema:
            type: string
        - name: start_timestamp
          in: query
          description: Start date for log range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: end_timestamp
          in: query
          description: End date for log range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: keyword
          in: query
          description: Search keyword
          schema:
            type: string
      responses:
        '200':
          description: Search response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Log'

  /log/self:
    get:
      tags:
        - Log
      summary: Get user logs
      description: Retrieves logs for the currently authenticated user
      security:
        - bearerAuth: []
      parameters:
        - name: p
          in: query
          description: Page number
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Items per page
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: Logs response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Log'

  /log/self/search:
    get:
      tags:
        - Log
      summary: Search user logs
      description: Searches logs for the currently authenticated user with filters
      security:
        - bearerAuth: []
      parameters:
        - name: p
          in: query
          description: Page number
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Items per page
          schema:
            type: integer
            default: 10
        - name: token_name
          in: query
          description: Filter by token name
          schema:
            type: string
        - name: model
          in: query
          description: Filter by model
          schema:
            type: string
        - name: start_timestamp
          in: query
          description: Start date for log range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: end_timestamp
          in: query
          description: End date for log range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: keyword
          in: query
          description: Search keyword
          schema:
            type: string
      responses:
        '200':
          description: Search response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Log'

  /data:
    get:
      tags:
        - Log
      summary: Get quota data
      description: Retrieves quota usage data (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: username
          in: query
          description: Filter by username
          schema:
            type: string
        - name: start_timestamp
          in: query
          description: Start date for data range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: end_timestamp
          in: query
          description: End date for data range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: default_time
          in: query
          description: Default time range
          schema:
            type: string
      responses:
        '200':
          description: Data response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      quota_data:
                        type: array
                        items:
                          type: object
                          properties:
                            model:
                              type: string
                            quota:
                              type: number
                              format: float
                            tokens:
                              type: integer
                            times:
                              type: integer
                      distribution_data:
                        type: array
                        items:
                          type: object
                          properties:
                            date:
                              type: string
                            quota:
                              type: number
                              format: float
                            tokens:
                              type: integer
                            times:
                              type: integer

  /data/self:
    get:
      tags:
        - Log
      summary: Get user quota data
      description: Retrieves quota usage data for the currently authenticated user
      security:
        - bearerAuth: []
      parameters:
        - name: start_timestamp
          in: query
          description: Start date for data range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: end_timestamp
          in: query
          description: End date for data range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: default_time
          in: query
          description: Default time range
          schema:
            type: string
      responses:
        '200':
          description: Data response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      quota_data:
                        type: array
                        items:
                          type: object
                          properties:
                            model:
                              type: string
                            quota:
                              type: number
                              format: float
                            tokens:
                              type: integer
                            times:
                              type: integer
                      distribution_data:
                        type: array
                        items:
                          type: object
                          properties:
                            date:
                              type: string
                            quota:
                              type: number
                              format: float
                            tokens:
                              type: integer
                            times:
                              type: integer

  /mj:
    get:
      tags:
        - Midjourney
      summary: Get all Midjourney logs
      description: Retrieves all Midjourney image generation logs (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: p
          in: query
          description: Page number
          schema:
            type: integer
            default: 0
        - name: mj_id
          in: query
          description: Filter by Midjourney ID
          schema:
            type: string
        - name: channel_id
          in: query
          description: Filter by channel ID
          schema:
            type: string
        - name: start_timestamp
          in: query
          description: Start date for log range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: end_timestamp
          in: query
          description: End date for log range (unix timestamp)
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Midjourney logs response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Midjourney'

  /mj/self:
    get:
      tags:
        - Midjourney
      summary: Get user Midjourney logs
      description: Retrieves Midjourney image generation logs for the currently authenticated user
      security:
        - bearerAuth: []
      parameters:
        - name: p
          in: query
          description: Page number
          schema:
            type: integer
            default: 0
        - name: mj_id
          in: query
          description: Filter by Midjourney ID
          schema:
            type: string
        - name: start_timestamp
          in: query
          description: Start date for log range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: end_timestamp
          in: query
          description: End date for log range (unix timestamp)
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Midjourney logs response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Midjourney'

  /mj/image/{id}:
    get:
      tags:
        - Midjourney
      summary: Get Midjourney image
      description: Retrieves a specific Midjourney image by its ID
      parameters:
        - name: id
          in: path
          required: true
          description: Midjourney ID
          schema:
            type: string
      responses:
        '200':
          description: Image response
          content:
            image/*:
              schema:
                type: string
                format: binary

  /status:
    get:
      tags:
        - System
      summary: Get system status
      description: Retrieves system status information
      responses:
        '200':
          description: Status response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      system_name:
                        type: string
                      version:
                        type: string
                      start_time:
                        type: integer
                        format: int64
                      email_verification:
                        type: boolean
                      github_oauth:
                        type: boolean
                      oidc:
                        type: boolean
                      wechat_login:
                        type: boolean
                      turnstile_check:
                        type: boolean
                      telegram_oauth:
                        type: boolean
                      linuxdo_oauth:
                        type: boolean

  /notice:
    get:
      tags:
        - System
      summary: Get system notice
      description: Retrieves system notice
      responses:
        '200':
          description: Notice response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: string
                    description: Notice content in markdown format

  /home_page_content:
    get:
      tags:
        - System
      summary: Get home page content
      description: Retrieves custom home page content
      responses:
        '200':
          description: Home page content response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: string
                    description: Home page content in markdown format or URL

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    Log:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        username:
          type: string
        token_name:
          type: string
        model:
          type: string
        prompt_tokens:
          type: integer
        completion_tokens:
          type: integer
        total_tokens:
          type: integer
        quota:
          type: number
          format: float
        created_at:
          type: string
          format: date-time
        status:
          type: string
        request:
          type: string
        response:
          type: string
    Midjourney:
      type: object
      properties:
        id:
          type: integer
        code:
          type: integer
        user_id:
          type: integer
        action:
          type: string
        mj_id:
          type: string
        prompt:
          type: string
        prompt_en:
          type: string
        description:
          type: string
        state:
          type: string
        submit_time:
          type: integer
          format: int64
        start_time:
          type: integer
          format: int64
        finish_time:
          type: integer
          format: int64
        image_url:
          type: string
        status:
          type: string
        progress:
          type: string
        fail_reason:
          type: string
        quota:
          type: number
          format: float
    Task:
      type: object
      properties:
        id:
          type: integer
        user_id:
          type: integer
        type:
          type: string
        status:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        details:
          type: string

  /task:
    get:
      tags:
        - Task
      summary: Get all tasks
      description: Retrieves all tasks (admin only)
      security:
        - bearerAuth: []
      parameters:
        - name: p
          in: query
          description: Page number
          schema:
            type: integer
            default: 0
        - name: task_id
          in: query
          description: Filter by task ID
          schema:
            type: string
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
        - name: start_timestamp
          in: query
          description: Start date for task range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: end_timestamp
          in: query
          description: End date for task range (unix timestamp)
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Tasks response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Task'

  /task/self:
    get:
      tags:
        - Task
      summary: Get user tasks
      description: Retrieves tasks for the currently authenticated user
      security:
        - bearerAuth: []
      parameters:
        - name: p
          in: query
          description: Page number
          schema:
            type: integer
            default: 0
        - name: task_id
          in: query
          description: Filter by task ID
          schema:
            type: string
        - name: status
          in: query
          description: Filter by status
          schema:
            type: string
        - name: start_timestamp
          in: query
          description: Start date for task range (unix timestamp)
          schema:
            type: integer
            format: int64
        - name: end_timestamp
          in: query
          description: End date for task range (unix timestamp)
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Tasks response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Task'