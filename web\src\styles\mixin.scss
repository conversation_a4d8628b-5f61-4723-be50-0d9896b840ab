@mixin shadow-0 {
  box-shadow: none;
}
@mixin shadow-1 {
  box-shadow: none;
}
@mixin shadow-2 {
  box-shadow:
    0px 2px 4px 0px rgba(0, 0, 0, 0.14),
    0px 0px 1px 0px rgba(0, 0, 0, 0.16);
}
@mixin shadow-knob {
  box-shadow:
    0px 4px 6px 0px rgba(0, 0, 0, 0.1),
    0px 0px 1px 0px rgba(0, 0, 0, 0.3);
}
@mixin shadow-elevated {
  box-shadow:
    0px 0px 1px 0px rgba(0, 0, 0, 0.3),
    0px 4px 14px 0px rgba(0, 0, 0, 0.1);
}

@mixin box-sizing() {
  box-sizing: border-box;
}

@mixin all-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin ver-center {
  display: flex;
  align-items: center;
}

@mixin hor-center {
  display: flex;
  justify-content: center;
}

@mixin text-overflow-hidden {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin no-scrollbar {
  &::-webkit-scrollbar {
    // width: 8px;
    display: none;
  }
  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    // background-color: $grey-2;
  }
  &::-webkit-scrollbar-track {
    opacity: 0;
    background-color: rgba(0, 0, 0, 0.05);
    background-color: transparent;
    // background-color: $grey-0;
  }
  &:hover {
    &::-webkit-scrollbar {
      display: block;
      width: 8px;
      // width: 12px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background-color: var(--semi-gray-3);
    }
    &::-webkit-scrollbar-track {
      // background-color: rgba(0, 0, 0, 0.05);
    }
  }
}
