export const CHANNEL_OPTIONS = [
  { value: 1, color: 'green', label: 'OpenAI' },
  {
    value: 2,
    color: 'light-blue',
    label: 'Midjourney Proxy',
  },
  {
    value: 5,
    color: 'blue',
    label: 'Midjourney Proxy Plus',
  },
  {
    value: 36,
    color: 'purple',
    label: 'Suno API',
  },
  { value: 4, color: 'grey', label: 'Ollama' },
  {
    value: 14,
    color: 'indigo',
    label: 'Anthropic Claude',
  },
  {
    value: 33,
    color: 'indigo',
    label: 'AWS Claude',
  },
  { value: 41, color: 'blue', label: 'Vertex AI' },
  {
    value: 3,
    color: 'teal',
    label: 'Azure OpenAI',
  },
  {
    value: 34,
    color: 'purple',
    label: 'Cohere',
  },
  { value: 39, color: 'grey', label: 'Cloudflare' },
  { value: 43, color: 'blue', label: 'DeepSeek' },
  {
    value: 15,
    color: 'blue',
    label: '百度文心千帆',
  },
  {
    value: 46,
    color: 'blue',
    label: '百度文心千帆V2',
  },
  {
    value: 17,
    color: 'orange',
    label: '阿里通义千问',
  },
  {
    value: 18,
    color: 'blue',
    label: '讯飞星火认知',
  },
  {
    value: 16,
    color: 'violet',
    label: '智谱 ChatGLM',
  },
  {
    value: 26,
    color: 'purple',
    label: '智谱 GLM-4V',
  },
  {
    value: 24,
    color: 'orange',
    label: 'Google Gemini',
  },
  {
    value: 11,
    color: 'orange',
    label: 'Google PaLM2',
  },
  {
    value: 47,
    color: 'blue',
    label: 'Xinference',
  },
  { value: 25, color: 'green', label: 'Moonshot' },
  { value: 20, color: 'green', label: 'OpenRouter' },
  { value: 49, color: 'green', label: 'GitHub Models' },
  { value: 19, color: 'blue', label: '360 智脑' },
  { value: 23, color: 'teal', label: '腾讯混元' },
  { value: 31, color: 'green', label: '零一万物' },
  { value: 35, color: 'green', label: 'MiniMax' },
  { value: 37, color: 'teal', label: 'Dify' },
  { value: 38, color: 'blue', label: 'Jina' },
  { value: 40, color: 'purple', label: 'SiliconCloud' },
  { value: 42, color: 'blue', label: 'Mistral AI' },
  { value: 8, color: 'pink', label: '自定义渠道' },
  {
    value: 22,
    color: 'blue',
    label: '知识库：FastGPT',
  },
  {
    value: 21,
    color: 'purple',
    label: '知识库：AI Proxy',
  },
  {
    value: 44,
    color: 'purple',
    label: '嵌入模型：MokaAI M3E',
  },
  {
    value: 45,
    color: 'blue',
    label: '字节火山方舟、豆包、DeepSeek通用',
  },
  {
    value: 48,
    color: 'blue',
    label: 'xAI'
  }
];
